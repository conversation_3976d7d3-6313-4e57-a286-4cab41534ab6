package com.wheat.dto;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

@Data
public class AgriculturalTaskDTO {
    // 分页参数
    private Long current = 1L;  // 当前页码，默认第1页
    private Long size = 10L;    // 每页大小，默认10条

    // 查询条件
    private Integer id;
    private Integer schemeId;
    private String stage;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    private Date endDate;
    
    private String taskName;
    private Date taskDate;
    private String condition;
}
