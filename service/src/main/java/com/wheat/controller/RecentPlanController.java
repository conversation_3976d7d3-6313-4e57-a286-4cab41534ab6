package com.wheat.controller;

import com.wheat.dto.RecentPlanDTO;
import com.wheat.exception.WheatException;
import com.wheat.result.PageResult;
import com.wheat.result.Result;
import com.wheat.service.RecentPlansService;
import com.wheat.vo.RecentPlanVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/recent_plans")
public class RecentPlanController {
    @Autowired
    private RecentPlansService recentPlansService;
    @PostMapping("/page")
    public Result<PageResult<RecentPlanVo>> getRecentPlanListPage(@RequestBody RecentPlanDTO dto) {
        try{
            if (dto !=null) {
                return Result.ok(recentPlansService.getRecentPlansListPage(dto));
            }else{
                return Result.fail(40001,"参数错误");
            }
        }catch (WheatException e)
            {
            return Result.fail(e.getCode(),e.getMessage());
        }
    }
    @PostMapping("/search")
    public Result<PageResult<RecentPlanVo>> getRecentPlanListBySearch(@RequestBody RecentPlanDTO dto) {
        try{
            if (dto !=null) {
                PageResult<RecentPlanVo> result = recentPlansService.getRecentPlanListBySearch(dto);
                return Result.ok(result);
            }else{
                return Result.fail(40001,"参数错误");
            }
        }catch (WheatException e)
            {
            return Result.fail(e.getCode(),e.getMessage());
        }
    }
    @PostMapping("/insert")
    public Result insertRecentPlan(@RequestBody RecentPlanDTO dto) {
        try{
            if (dto !=null) {
                recentPlansService.insertRecentPlan(dto);
                return Result.ok();
            }else{
                return Result.fail(40001,"参数错误");
            }
        }catch (WheatException e)
            {
            return Result.fail(e.getCode(),e.getMessage());
        }
    }
    @PostMapping("/update")
    public Result updateRecentPlan(@RequestBody RecentPlanDTO dto) {
        try{
            if (dto !=null) {
                recentPlansService.updateRecentPlan(dto);
                return Result.ok();
            }else{
                return Result.fail(40001,"参数错误");
            }
        }catch (WheatException e)
                {
            return Result.fail(e.getCode(),e.getMessage());
        }
    }
    @PostMapping("/delete")
    public Result deleteRecentPlan(@RequestBody RecentPlanDTO dto) {
        try{
            if (dto !=null) {
                recentPlansService.deleteRecentPlan(dto.getId());
                return Result.ok();
            }else{
                return Result.fail(40001,"参数错误");
            }
        }catch (WheatException e)
                {
            return Result.fail(e.getCode(),e.getMessage());
        }
    }
}

