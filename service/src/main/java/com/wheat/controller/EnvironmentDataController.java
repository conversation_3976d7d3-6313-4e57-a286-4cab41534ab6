package com.wheat.controller;

import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.dto.EnvironmentDataDTO;
import com.wheat.exception.WheatException;
import com.wheat.result.PageResult;
import com.wheat.result.Result;
import com.wheat.service.EnvironmentDataService;
import com.wheat.vo.EnvironmentDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/environment_data")
public class EnvironmentDataController {
    @Autowired
    private EnvironmentDataService service;
    @PostMapping("/page")
    public Result<PageResult<EnvironmentDataVo>> getEnvironmentDataPageList(@RequestBody EnvironmentDataDTO dto){
        try{
            if(dto.getCurrent() == null || dto.getCurrent()<=0) {
                dto.setCurrent(1L);
                dto.setSize(10L);
            }
            PageResult<EnvironmentDataVo> page = service.getEnvironmentDataList(dto);
            return Result.ok(page);
        }catch (WheatException e){
            return Result.fail(e.getCode(),e.getMessage());
        }

    }
    @PostMapping("/insert")
    public Result insertEnvironmentData(@RequestBody(required = false) EnvironmentDataDTO dto) {
        if (dto == null) {
            return Result.fail(201, "新增数据不能为空");
        }
        try {
            // 打印接收到的数据
            System.out.println("接收到的数据: " + dto);
            
            service.insertEnvironmentData(dto);
            return Result.ok();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof WheatException) {
                WheatException we = (WheatException) e;
                return Result.fail(we.getCode(), we.getMessage());
            } else {
                return Result.fail(201, "插入数据失败: " + e.getMessage());
            }
        }
    }
    @PostMapping("/update")
    public Result updateEnvironmentData(@RequestBody(required = false) EnvironmentDataDTO dto) {
        if (dto == null) {
            return Result.fail(201, "更新数据不能为空");
        }
        try {
            service.updateEnvironmentData(dto);
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @PostMapping("/delete")
    public Result deleteEnvironmentData(@RequestBody(required = false) EnvironmentDataDTO dto) {
        if (dto == null) {
            return Result.fail(201, "删除数据id不能为空");
        }
        try {
            service.deleteEnvironmentData(dto.getId());
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @PostMapping("/search")
    public Result<PageResult<EnvironmentDataVo>> getEnvironmentDataBySearch(@RequestBody(required = false) EnvironmentDataDTO dto) {
        if (dto == null) {
            return Result.fail(201, "搜索条件不能为空");
        }
        try {
            PageResult<EnvironmentDataVo> result = service.getEnvironmentDataListBySearch(dto);
            return Result.ok(result);
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
        }
}
