package com.wheat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.exception.WheatException;
import com.wheat.result.PageResult;
import com.wheat.result.Result;
import com.wheat.vo.AgriculturalTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.wheat.service.AgriculturalTaskService;
@RestController
@RequestMapping("/api/agricultural_task")
public class AgriculturalTaskController {
    @Autowired
    private AgriculturalTaskService service;

    @PostMapping("/page")
    public Result<PageResult<AgriculturalTaskVo>> getAgriculturalTaskList(@RequestBody AgriculturalTaskDTO dto) {
        try {
            if (dto.getCurrent() == null || dto.getCurrent() <= 0) {
                dto.setCurrent(1L);
                dto.setSize(10L);
            }
            PageResult<AgriculturalTaskVo> page = service.getAgriculturalTaskList(dto);
            return Result.ok(page);
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }

    @PostMapping("/byId")
    public Result<AgriculturalTaskVo> getAgriculturalTaskById(@RequestBody(required = false) AgriculturalTaskDTO dto) {
        if (dto == null) {
            return Result.fail(201, "请求体不能为空");
        }
        try {
            AgriculturalTaskVo result = service.getAgriculturalTaskListById(dto);
            return Result.ok(result);
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }

    @PostMapping("/search")
    public Result<PageResult<AgriculturalTaskVo>> getAgriculturalTaskBySearch(@RequestBody(required = false) AgriculturalTaskDTO dto) {
        if (dto == null) {
            return Result.fail(201, "搜索条件不能为空");
        }
        try {
            PageResult<AgriculturalTaskVo> result = service.getAgriculturalTaskBySearch(dto);
            return Result.ok(result);
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @PostMapping("/insert")
    public Result insertAgriculturalTask(@RequestBody AgriculturalTaskDTO dto) {
        if (dto == null) {
            return Result.fail(201, "插入数据不能为空");
        }
        try {
            service.insertAgriculturalTask(dto);
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @PostMapping("/delete")
    public Result deleteAgriculturalTask(@RequestBody AgriculturalTaskDTO dto) {
        if (dto == null) {
            return Result.fail(201, "删除数据不能为空");
        }
        try {
            service.deleteAgriculturalTask(dto);
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @PostMapping("/update")
    public Result updateAgriculturalTask(@RequestBody AgriculturalTaskDTO dto) {
        if (dto == null) {
            return Result.fail(201, "更新数据不能为空");
        }
        try {
            service.updateAgriculturalTask(dto);
            return Result.ok();
        }catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
}
