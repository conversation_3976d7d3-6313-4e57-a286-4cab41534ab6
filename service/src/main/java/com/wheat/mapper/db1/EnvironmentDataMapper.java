package com.wheat.mapper.db1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.EnvironmentDataDTO;
import com.wheat.entity.EnvironmentData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wheat.vo.EnvironmentDataVo;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【environment_data】的数据库操作Mapper
* @createDate 2025-06-23 11:22:32
* @Entity generator.entity.EnvironmentData
*/
public interface EnvironmentDataMapper extends BaseMapper<EnvironmentData> {
    IPage<EnvironmentDataVo> getEnvironmentDataListPage(@Param("page") IPage<EnvironmentDataVo> page, @Param("dto") EnvironmentDataDTO dto);
    IPage<EnvironmentDataVo> getEnvironmentDataListBySearch(@Param("dto") EnvironmentDataDTO dto, IPage<EnvironmentDataVo> page);
}




