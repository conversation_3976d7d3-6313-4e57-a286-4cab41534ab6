package com.wheat.mapper.db1;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.entity.AgriculturalTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wheat.result.Result;
import com.wheat.vo.AgriculturalTaskVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【agricultural_task】的数据库操作Mapper
* @createDate 2025-06-23 11:10:42
* @Entity generator.entity.AgriculturalTask
*/
public interface AgriculturalTaskMapper extends BaseMapper<AgriculturalTask> {

    /**
     * 分页查询农业任务列表
     * @param page 分页对象
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<AgriculturalTaskVo> getAgriculturalTaskListPage(@Param("page") IPage<AgriculturalTaskVo> page, @Param("dto") AgriculturalTaskDTO dto);
    AgriculturalTaskVo getAgriculturalTaskListById(@Param("dto") AgriculturalTaskDTO dto);
    IPage<AgriculturalTaskVo> getAgriculturalTaskListBySearch(@Param("dto") AgriculturalTaskDTO dto, IPage<AgriculturalTaskVo> page);
    void insertAgriculturalTask(AgriculturalTaskDTO dto);
}




