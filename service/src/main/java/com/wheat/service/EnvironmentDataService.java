package com.wheat.service;

import com.wheat.dto.EnvironmentDataDTO;
import com.wheat.entity.EnvironmentData;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wheat.result.PageResult;
import com.wheat.vo.EnvironmentDataVo;

/**
* <AUTHOR>
* @description 针对表【environment_data】的数据库操作Service
* @createDate 2025-06-23 11:22:32
*/
public interface EnvironmentDataService extends IService<EnvironmentData> {
    PageResult<EnvironmentDataVo> getEnvironmentDataList(EnvironmentDataDTO dto);
    void insertEnvironmentData(EnvironmentDataDTO dto);
    void updateEnvironmentData(EnvironmentDataDTO dto);
    void deleteEnvironmentData(Integer id);
    PageResult<EnvironmentDataVo> getEnvironmentDataListBySearch(EnvironmentDataDTO dto);
}
