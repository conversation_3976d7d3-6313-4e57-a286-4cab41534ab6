package com.wheat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.RecentPlanDTO;
import com.wheat.entity.RecentPlans;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wheat.result.PageResult;
import com.wheat.result.Result;
import com.wheat.vo.RecentPlanVo;

/**
* <AUTHOR>
* @description 针对表【recent_plans】的数据库操作Service
* @createDate 2025-06-23 11:22:46
*/
public interface RecentPlansService extends IService<RecentPlans> {
    PageResult<RecentPlanVo> getRecentPlansListPage(RecentPlanDTO dto);
    void insertRecentPlan(RecentPlanDTO dto);
    void updateRecentPlan(RecentPlanDTO dto);
    void deleteRecentPlan(Integer id);
    IPage<RecentPlanVo> getRecentPlanListBySearch(RecentPlanDTO dto);
}
