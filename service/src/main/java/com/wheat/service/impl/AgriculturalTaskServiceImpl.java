package com.wheat.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.entity.AgriculturalTask;
import com.wheat.exception.WheatException;
import com.wheat.result.PageResult;
import com.wheat.result.Result;
import com.wheat.service.AgriculturalTaskService;
import com.wheat.mapper.db1.AgriculturalTaskMapper;
import com.wheat.vo.AgriculturalTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【agricultural_task】的数据库操作Service实现
* @createDate 2025-06-23 11:10:42
*/
@Service
public class AgriculturalTaskServiceImpl extends ServiceImpl<AgriculturalTaskMapper, AgriculturalTask>
    implements AgriculturalTaskService {
    @Autowired
    private AgriculturalTaskMapper agriculturalTaskMapper;

    @Override
    public PageResult<AgriculturalTaskVo> getAgriculturalTaskList(AgriculturalTaskDTO dto) {
        // 创建分页对象
        IPage<AgriculturalTaskVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        PageResult<AgriculturalTaskVo> result = new PageResult<>();
        IPage<AgriculturalTaskVo> pageResult = agriculturalTaskMapper.getAgriculturalTaskListPage(page, dto);
        result.setRecords(pageResult.getRecords());
        result.setCurrent(dto.getCurrent());
        result.setSize(dto.getSize());
        result.setTotal(pageResult.getTotal());
        return result;
    }

    @Override
    public AgriculturalTaskVo getAgriculturalTaskListById(AgriculturalTaskDTO dto) {
        return agriculturalTaskMapper.getAgriculturalTaskListById(dto);
    }
    @Override
    public PageResult<AgriculturalTaskVo> getAgriculturalTaskBySearch(AgriculturalTaskDTO dto) {
        PageResult<AgriculturalTaskVo> result = new PageResult<>();
        IPage<AgriculturalTaskVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        IPage<AgriculturalTaskVo> pageResult = agriculturalTaskMapper.getAgriculturalTaskListBySearch(dto, page);
        result.setRecords(pageResult.getRecords());
        result.setCurrent(dto.getCurrent());
        result.setSize(dto.getSize());
        result.setTotal(pageResult.getTotal());
        return result;
    }
    @Override
    public Result insertAgriculturalTask(AgriculturalTaskDTO dto) {
        try {
            agriculturalTaskMapper.insertAgriculturalTask(dto);
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @Override
    public Result deleteAgriculturalTask(AgriculturalTaskDTO dto) {
        try {
            Integer id=dto.getId();
            agriculturalTaskMapper.deleteById(id);
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
    @Override
    public Result updateAgriculturalTask(AgriculturalTaskDTO dto) {
        try {
            Integer id = dto.getId();
            UpdateWrapper<AgriculturalTask> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("scheme_id", dto.getSchemeId());
            updateWrapper.set("stage", dto.getStage());
            updateWrapper.set("start_date", dto.getStartDate());
            updateWrapper.set("end_date", dto.getEndDate());
            updateWrapper.set("water_usage", dto.getWaterUsage());
            updateWrapper.set("nitrogen_usage", dto.getNitrogenUsage());
            updateWrapper.set("phosphorus_usage", dto.getPhosphorusUsage());
            updateWrapper.set("potassium_usage", dto.getPotassiumUsage());
            updateWrapper.set("task_name", dto.getTaskName());
            updateWrapper.set("task_date", dto.getTaskDate());
            updateWrapper.set("`condition`", dto.getCondition());
            update(updateWrapper);
            return Result.ok();
        } catch (WheatException e) {
            return Result.fail(e.getCode(), e.getMessage());
        }
    }
}




