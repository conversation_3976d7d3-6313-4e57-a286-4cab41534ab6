package com.wheat.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wheat.dto.RecentPlanDTO;
import com.wheat.entity.RecentPlans;
import com.wheat.result.PageResult;
import com.wheat.result.Result;
import com.wheat.service.RecentPlansService;
import com.wheat.mapper.db1.RecentPlansMapper;
import com.wheat.vo.RecentPlanVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【recent_plans】的数据库操作Service实现
* @createDate 2025-06-23 11:22:46
*/
@Service
public class RecentPlansServiceImpl extends ServiceImpl<RecentPlansMapper, RecentPlans>
    implements RecentPlansService{
    @Autowired
    private RecentPlansMapper recentPlansMapper;
    @Override
    public PageResult<RecentPlanVo> getRecentPlansListPage(RecentPlanDTO dto) {
        // 创建分页对象
        IPage<RecentPlanVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        PageResult<RecentPlanVo> result = new PageResult<>();
        IPage<RecentPlanVo> pageResult = recentPlansMapper.getRecentPlansListPage(page);
        result.setRecords(pageResult.getRecords());
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(pageResult.getTotal());
        return result;
    }
    @Override
    public void insertRecentPlan(RecentPlanDTO dto) {
        RecentPlans recentPlans = new RecentPlans();
        recentPlans.setId(null); // 确保ID为null，使用数据库自增
        recentPlans.setPlanItem(dto.getPlanItem());
        recentPlans.setTitle(dto.getTitle());
        save(recentPlans);
    }
    @Override
    public void updateRecentPlan(RecentPlanDTO dto) {
        UpdateWrapper<RecentPlans> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", dto.getId());
        RecentPlans recentPlans = new RecentPlans();
        recentPlans.setPlanItem(dto.getPlanItem());
        recentPlans.setTitle(dto.getTitle());
        update(recentPlans,updateWrapper);
    }
    @Override
    public void deleteRecentPlan(Integer id) {
        UpdateWrapper<RecentPlans> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        remove(updateWrapper);
    }
    @Override
    public PageResult<RecentPlanVo> getRecentPlanListBySearch(RecentPlanDTO dto) {
        IPage<RecentPlanVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        IPage<RecentPlanVo> pageResult = recentPlansMapper.getRecentPlanListBySearch(page, dto);
        PageResult<RecentPlanVo> result = new PageResult<>();
        result.setRecords(pageResult.getRecords());
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(pageResult.getTotal());
        return result;
    }
}




