package com.wheat.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wheat.dto.EnvironmentDataDTO;
import com.wheat.entity.EnvironmentData;
import com.wheat.exception.WheatException;
import com.wheat.result.PageResult;
import com.wheat.service.EnvironmentDataService;
import com.wheat.mapper.db1.EnvironmentDataMapper;
import com.wheat.vo.EnvironmentDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @description 针对表【environment_data】的数据库操作Service实现
* @createDate 2025-06-23 11:22:32
*/
@Service
public class EnvironmentDataServiceImpl extends ServiceImpl<EnvironmentDataMapper, EnvironmentData>
    implements EnvironmentDataService{
    @Autowired
    private EnvironmentDataMapper environmentDataMapper;
    @Override
    public PageResult<EnvironmentDataVo> getEnvironmentDataList(EnvironmentDataDTO dto) {
        IPage<EnvironmentDataVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        IPage<EnvironmentDataVo> result = environmentDataMapper.getEnvironmentDataListPage(page, dto);
        PageResult<EnvironmentDataVo> pageResult = new PageResult<>();
        pageResult.setRecords(result.getRecords());
        pageResult.setCurrent(dto.getCurrent());
        pageResult.setSize(dto.getSize());
        pageResult.setTotal(result.getTotal());
        return pageResult;
    }
    @Override
    public void insertEnvironmentData(EnvironmentDataDTO dto) {
        try {
            EnvironmentData environmentData = new EnvironmentData();
            environmentData.setId(null); // 确保ID为null，使用数据库自增
            environmentData.setTemperature(dto.getTemperature());
            environmentData.setHumidity(dto.getHumidity());
            environmentData.setSoilMoisture(dto.getSoilMoisture());
            environmentData.setRecordTime(dto.getRecordTime());
            save(environmentData);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof WheatException) {
                throw (WheatException) e;
            } else {
                throw new WheatException(201, "插入环境数据异常: " + e.getMessage());
            }
        }
    }
    @Override
    public void updateEnvironmentData(EnvironmentDataDTO dto) {
        try {
            Integer id = dto.getId();
            UpdateWrapper<EnvironmentData> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("temperature", dto.getTemperature());
            updateWrapper.set("humidity", dto.getHumidity());
            updateWrapper.set("soil_moisture", dto.getSoilMoisture());
            updateWrapper.set("record_time", dto.getRecordTime());
            update(updateWrapper);

        } catch (Exception e) {e.printStackTrace();
            if (e instanceof WheatException) {
                throw (WheatException) e;
            } else {
                throw new WheatException(201, "更新环境数据异常: " + e.getMessage());
            }
        }
    }
    @Override
    @Transactional
    public void deleteEnvironmentData(Integer id) {
        try {
            removeById(id);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof WheatException) {
                throw (WheatException) e;
            } else {
                throw new WheatException(201, "删除环境数据异常: " + e.getMessage());
            }
        }
    }
    @Override
    public PageResult<EnvironmentDataVo> getEnvironmentDataListBySearch(EnvironmentDataDTO dto) {
        IPage<EnvironmentDataVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        IPage<EnvironmentDataVo> result = environmentDataMapper.getEnvironmentDataListBySearch(dto, page);
        PageResult<EnvironmentDataVo> pageResult = new PageResult<>();
        pageResult.setRecords(result.getRecords());
        pageResult.setCurrent(dto.getCurrent());
        pageResult.setSize(dto.getSize());
        pageResult.setTotal(result.getTotal());
        return pageResult;
    }
}




