<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wheat.mapper.db1.RecentPlansMapper">

    <resultMap id="BaseResultMap" type="com.wheat.entity.RecentPlans">
            <id property="id" column="id" />
            <result property="planItem" column="plan_item" />
            <result property="title" column="title" />
    </resultMap>

    <sql id="Base_Column_List">
        id,plan_item,title
    </sql>
    <select id="getRecentPlansListPage" resultType="com.wheat.vo.RecentPlanVo">
            select
            <include refid="Base_Column_List" />
            from recent_plans
            order by id desc
    </select>
    <select id="getRecentPlanListBySearch" resultType="com.wheat.vo.RecentPlanVo">
            select
            <include refid="Base_Column_List" />
            from recent_plans
            where title like concat('%',#{dto.title},'%')
            order by id desc
    </select>
</mapper>
