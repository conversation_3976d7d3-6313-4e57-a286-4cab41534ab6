<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wheat.mapper.db1.EnvironmentDataMapper">

    <resultMap id="BaseResultMap" type="com.wheat.entity.EnvironmentData">
            <id property="id" column="id" />
            <result property="temperature" column="temperature" />
            <result property="humidity" column="humidity" />
            <result property="soilMoisture" column="soil_moisture" />
            <result property="recordTime" column="record_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,temperature,humidity,soil_moisture,record_time
    </sql>
    <select id="getEnvironmentDataListPage" resultType="com.wheat.vo.EnvironmentDataVo">
        SELECT
        id,
        temperature,
        humidity,
        soil_moisture as soilMoisture,
        record_time as recordTime
        FROM environment_data
        <where>
            <if test="dto.id != null">s
                AND id = #{dto.id}
            </if>
            <if test="dto.temperature != null">
                AND temperature = #{dto.temperature}
            </if>
            <if test="dto.humidity != null">
                AND humidity = #{dto.humidity}
            </if>
            <if test="dto.soilMoisture != null">
                AND soil_moisture = #{dto.soilMoisture}
            </if>
            <if test="dto.recordTime != null">
                AND record_time = #{dto.recordTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>
    <select id="getEnvironmentDataListBySearch" resultType="com.wheat.vo.EnvironmentDataVo">
        SELECT
        <include refid="Base_Column_List" />
        FROM environment_data
        <where>
            <if test="dto.temperature != null">
                AND temperature &gt;= #{dto.minTemperature}
            </if>
            <if test="dto.temperature != null">
                AND temperature &lt;= #{dto.maxTemperature}
            </if>
            <if test="dto.humidity != null">
                AND humidity &gt;= #{dto.minHumidity}
            </if>
            <if test="dto.humidity != null">
                AND humidity &lt;= #{dto.maxHumidityhumidity}
            </if>
            <if test="dto.soilMoisture != null">
                AND soil_moisture &gt;= #{dto.minSoilMoisture}
            </if>
            <if test="dto.soilMoisture != null">
                AND soil_moisture &lt;= #{dto.maxSoilMoisture}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
